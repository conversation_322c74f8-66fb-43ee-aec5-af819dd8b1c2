import React from 'react';
import { Link, useNavigate, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import MobileNav from "./MobileNav";
import { AuthButton } from "@/components/auth/AuthButton";


interface MenuItem {
  title: string;
  href: string;
}

const Navbar = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Updated menu items
  const menuItems: MenuItem[] = [
    { title: "Home", href: "/" },
    { title: "Software", href: "/software" },
    { title: "Hardware", href: "/hardware" },
    { title: "Resellers", href: "/resellers" },
    { title: "What's New", href: "/whats-new" },
    { title: "Contact", href: "/contact" },
  ];

  // Function to get navbar colors based on current page
  const getNavbarColors = () => {
    const currentPath = location.pathname;

    if (currentPath === '/') {
      // Home page - Purple theme
      return {
        background: 'bg-black/10 backdrop-blur-sm',
        accent: '#C084FC',
        hover: 'hover:bg-purple-500/5',
        active: 'bg-purple-500/10',
        text: 'text-purple-400',
        border: 'border-purple-500/5'
      };
    } else if (currentPath === '/software') {
      // Software page - Orange theme
      return {
        background: 'bg-black/10 backdrop-blur-sm',
        accent: '#F97316',
        hover: 'hover:bg-orange-500/5',
        active: 'bg-orange-500/10',
        text: 'text-orange-400',
        border: 'border-orange-500/5'
      };
    } else if (currentPath === '/hardware') {
      // Hardware page - Blue theme
      return {
        background: 'bg-black/10 backdrop-blur-sm',
        accent: '#3B82F6',
        hover: 'hover:bg-blue-500/5',
        active: 'bg-blue-500/10',
        text: 'text-blue-400',
        border: 'border-blue-500/5'
      };
    } else {
      // Default - Purple theme
      return {
        background: 'bg-black/10 backdrop-blur-sm',
        accent: '#C084FC',
        hover: 'hover:bg-purple-500/5',
        active: 'bg-purple-500/10',
        text: 'text-purple-400',
        border: 'border-purple-500/5'
      };
    }
  };

  const navbarColors = getNavbarColors();



  // Modified to handle section navigation and page navigation properly
  const scrollToSection = (href: string) => {
    if (href.startsWith('#')) {
      // Always try to scroll to section on current page first
      const element = document.getElementById(href.substring(1));
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
        return;
      }

      // If we're not on the home page and section wasn't found, navigate home with scrollTo
      if (location.pathname !== '/') {
        navigate('/', { state: { scrollTo: href.substring(1) } });
      }
    } else {
      // For non-hash links, use regular navigation
      navigate(href);
    }
  };

  return (
    <motion.header
      className={`fixed w-full z-50 top-0 ${navbarColors.background} transition-all duration-500`}
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <div className="container mx-auto px-6">
        <div className="flex items-center justify-between py-3">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3 group">
            <motion.div
              className="relative"
              whileHover={{
                scale: 1.05,
                filter: `drop-shadow(0 0 8px ${navbarColors.accent}20)`
              }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <svg
                viewBox="0 0 930 465"
                className="w-28 h-14 transition-all duration-300"
              >
                <motion.path
                  d="M166.7 114q3.7 0 6.7 2.1 47.6 27.2 94.6 55.4 0.8 39.6 0 79.2-30.3 18.6-61.3 36.3-3.8 2.7-7.1-0.4-0.4-37.5-0.8-75.1-16.5-10.3-33.4-20-0.4 79.2-0.8 158.4-1.4 2.1-3.7 2.5-30.6-17.4-60.9-35.4-3.1-1.5-4.6-4.6-0.8-77.5 0-155 1.7-4.6 6.3-3 29.1 17.1 58.3 34.2 2.4 1.6 5 1.7-1.4-36.4-0.4-73 0.8-1.8 2.1-3.3z"
                  fill={navbarColors.accent}
                />
              </svg>
            </motion.div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center justify-center flex-1">
            <nav className="flex items-center space-x-1">
              {menuItems.map((item, index) => (
                <motion.a
                  key={item.title}
                  href={item.href}
                  onClick={(e) => {
                    e.preventDefault();
                    scrollToSection(item.href);
                  }}
                  className={cn(
                    "relative font-medium text-sm transition-all duration-300 py-2 px-3 rounded-md",
                    "font-montserrat tracking-wide",
                    // Default state
                    "text-gray-300 hover:text-white",
                    // Hover and active states
                    navbarColors.hover,
                    (location.pathname === item.href ||
                     (location.pathname === '/' && item.href === '/')) &&
                    `${navbarColors.active} ${navbarColors.text}`
                  )}
                  whileHover={{ y: -1 }}
                  whileTap={{ scale: 0.98 }}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    delay: index * 0.1,
                    type: "spring",
                    stiffness: 300,
                    damping: 20
                  }}
                >
                  {item.title}
                  {/* Active indicator */}
                  {(location.pathname === item.href ||
                    (location.pathname === '/' && item.href === '/')) && (
                    <motion.div
                      className="absolute bottom-0 left-0 right-0 h-0.5 bg-current"
                      layoutId="navbar-indicator"
                      transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    />
                  )}
                </motion.a>
              ))}
            </nav>
          </div>

          {/* Authentication Buttons */}
          <div className="hidden md:flex items-center space-x-2">
            <AuthButton />
          </div>

          {/* Mobile Menu Button */}
          <div className="flex items-center space-x-2 md:hidden">
            <AuthButton />
            <MobileNav menuItems={menuItems} />
          </div>
        </div>
      </div>
    </motion.header>
  );
}

export default Navbar;
